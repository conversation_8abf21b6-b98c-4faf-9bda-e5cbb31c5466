<?php

namespace App\Events\K8s;

use App\Contracts\WebhookEventInterface;
use App\Models\Workspace;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BaseK8sResourceEvent implements WebhookEventInterface
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $namespace;

    public string $clusterName;

    public int $clusterId;

    public string $resourceType;

    public string $resourceName;

    public array $resource;

    public string $action; // created, updated, deleted

    public function __construct(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceType,
        string $resourceName,
        array $resource,
        string $action
    ) {
        $this->namespace = $namespace;
        $this->clusterName = $clusterName;
        $this->clusterId = $clusterId;
        $this->resourceType = $resourceType;
        $this->resourceName = $resourceName;
        $this->resource = $resource;
        $this->action = $action;
    }

    public function getWorkspace(): ?Workspace
    {
        return Workspace::where('namespace', $this->namespace)->first();
    }

    public function getEventType(): string
    {
        return "{$this->resourceType}.{$this->action}";
    }

    public function getEventPayload(): array
    {
        return [
            'event_type' => "{$this->resourceType}.{$this->action}",
            'namespace' => $this->namespace,
            'cluster' => [
                'id' => $this->clusterId,
                'name' => $this->clusterName,
            ],
            'resource' => [
                'type' => $this->resourceType,
                'name' => $this->resourceName,
                'data' => $this->resource,
            ],
            'action' => $this->action,
        ];
    }
}
