<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\Pod\PodNotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Resources\PodCommandResultResource;
use App\Models\Workspace;
use App\Service\PodService;
use App\Service\PodTerminalService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\StreamedResponse;

class PodController extends Controller
{
    public function __construct(
        protected PodTerminalService $podTerminalService
    ) {}

    /**
     * 获取 Pod 列表
     */
    public function index(Request $request): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'pods');

            // 使用缓存和并发锁获取数据
            $pods = $this->getCachedK8sData($cacheKey, function () use ($podService) {
                return $podService->getPods();
            });

            return $this->dto($pods);
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Pod 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取单个 Pod
     */
    public function show(Request $request, string $name): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            // 生成缓存键
            $cacheKey = $this->generateK8sCacheKey($workspace->id, 'pods', $name);

            // 使用缓存和并发锁获取数据
            $pod = $this->getCachedK8sData($cacheKey, function () use ($podService, $name) {
                return $podService->getPod($name);
            });

            return $this->dto($pod);
        } catch (PodNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Pod 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取 Pod 日志
     */
    public function logs(Request $request, string $name): JsonResponse
    {
        $validated = $request->validate([
            'container' => 'sometimes|string',
            'tail_lines' => 'sometimes|integer|min:1|max:1000',
            'timestamps' => 'sometimes|boolean',
            'previous' => 'sometimes|boolean',
        ]);

        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            $containerName = $validated['container'] ?? null;
            if (! $containerName) {
                // 生成缓存键获取 Pod 信息
                $podCacheKey = $this->generateK8sCacheKey($workspace->id, 'pods', $name);
                $pod = $this->getCachedK8sData($podCacheKey, function () use ($podService, $name) {
                    return $podService->getPod($name);
                });

                if (! empty($pod->containers)) {
                    $containerName = $pod->containers[0]['name'];
                }
            }

            if (! $containerName) {
                return $this->success(['logs' => '']);
            }

            $options = [
                'tail_lines' => $validated['tail_lines'] ?? 100,
                'timestamps' => $validated['timestamps'] ?? false,
                'previous' => $validated['previous'] ?? false,
            ];

            // 日志缓存时间较短，因为日志变化频繁
            $logsCacheKey = $this->generateK8sCacheKey(
                $workspace->id,
                'pod_logs',
                $name,
                array_merge(['container' => $containerName], $options)
            );

            $logs = $this->getCachedK8sData($logsCacheKey, function () use ($podService, $name, $containerName, $options) {
                return $podService->getPodLogs($name, $containerName, $options);
            }, 60); // 日志缓存 1 分钟

            return $this->success(['logs' => $logs]);
        } catch (PodNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('获取 Pod 日志失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 删除 Pod
     */
    public function destroy(Request $request, string $name): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            $podService->deletePod($name);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'pods');
            $this->clearK8sCache($workspace->id, 'deployments'); // Pod 删除可能影响 Deployment 状态
            $this->clearK8sCache($workspace->id, 'statefulsets'); // Pod 删除可能影响 StatefulSet 状态

            return $this->deleted();
        } catch (PodNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('删除 Pod 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 重启 Pod (通过删除重新创建)
     */
    public function restart(Request $request, string $name): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            $podService->restartPod($name);

            // 清除相关缓存
            $this->clearK8sCache($workspace->id, 'pods');
            $this->clearK8sCache($workspace->id, 'deployments'); // Pod 重启可能影响 Deployment 状态
            $this->clearK8sCache($workspace->id, 'statefulsets'); // Pod 重启可能影响 StatefulSet 状态

            return $this->success(['message' => "Pod '{$name}' 重启成功"]);
        } catch (PodNotFoundException $e) {
            return $this->notFound($e->getMessage());
        } catch (K8sConnectionException $e) {
            return $this->serviceUnavailable($e->getMessage());
        } catch (\Exception $e) {
            Log::error('重启 Pod 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 在 Pod 中执行命令
     * 支持普通模式和 SSE 流式模式
     */
    public function exec(Request $request, string $podName): JsonResponse|StreamedResponse
    {
        // 获取当前工作空间
        $workspace = $request->user()->getWorkspace();

        $maxLength = (128 * 1024 + 128) * 1024;

        // 验证请求数据
        $validated = $request->validate([
            'command' => 'required|array|min:1',
            'command.*' => 'required|string|max:'.$maxLength,
            'container' => 'nullable|string|max:255',
            'working_dir' => 'nullable|string|max:500',
            'env' => 'nullable|array',
            'env.*' => 'string|max:1000',
            'timeout' => 'nullable|integer|min:1|max:30',
            'stream' => 'nullable|boolean', // 是否使用 SSE 流式输出
        ]);

        // 检查是否使用流式输出
        $useStream = $validated['stream'] ?? false;

        if ($useStream) {
            return $this->executeWithStream($workspace, $podName, $validated);
        } else {
            return $this->executeNormal($workspace, $podName, $validated);
        }
    }

    /**
     * 普通模式执行命令
     */
    protected function executeNormal(Workspace $workspace, string $podName, array $validated): JsonResponse
    {
        try {
            $result = $this->podTerminalService->executeCommand(
                workspace: $workspace,
                podName: $podName,
                containerName: $validated['container'] ?? null,
                command: $validated['command'],
                workingDir: $validated['working_dir'] ?? null,
                env: $validated['env'] ?? null,
                timeout: $validated['timeout'] ?? 30
            );

            return response()->json(
                new PodCommandResultResource($result),
                $result->isSuccess() ? 200 : 422
            );
        } catch (\Exception $e) {
            Log::error('Pod command execution failed', [
                'workspace_id' => $workspace->id,
                'pod_name' => $podName,
                'error' => $e->getMessage(),
            ]);

            return $this->serverError($e->getMessage());
        }
    }

    /**
     * 流式模式执行命令（SSE）
     */
    protected function executeWithStream(Workspace $workspace, string $podName, array $validated): StreamedResponse
    {
        return response()->stream(function () use ($workspace, $podName, $validated) {
            try {
                // 设置 SSE 开始标记
                echo 'data: '.json_encode([
                    'type' => 'start',
                    'message' => 'Command execution started',
                    'timestamp' => microtime(true),
                ])."\n\n";

                // 强制刷新输出缓冲区
                $this->flushOutput();

                // 创建实时输出回调
                $outputCallback = function (string $type, string $data) {
                    $output = json_encode([
                        'type' => $type,
                        'data' => $data,
                        'timestamp' => microtime(true),
                    ]);

                    echo "data: {$output}\n\n";
                    $this->flushOutput();
                };

                // 执行命令
                $result = $this->podTerminalService->executeCommandWithRealTimeOutput(
                    workspace: $workspace,
                    podName: $podName,
                    containerName: $validated['container'] ?? null,
                    command: $validated['command'],
                    outputCallback: $outputCallback,
                    workingDir: $validated['working_dir'] ?? null,
                    env: $validated['env'] ?? null,
                    timeout: $validated['timeout'] ?? 30
                );

                // 发送最终结果
                echo 'data: '.json_encode([
                    'type' => 'result',
                    'data' => $result->toArray(),
                    'timestamp' => microtime(true),
                ])."\n\n";

                // 发送结束标记
                echo 'data: '.json_encode([
                    'type' => 'end',
                    'timestamp' => microtime(true),
                ])."\n\n";

                $this->flushOutput();

            } catch (\Exception $e) {
                Log::error('Pod command execution failed', [
                    'workspace_id' => $workspace->id,
                    'pod_name' => $podName,
                    'error' => $e->getMessage(),
                ]);

                // 发送错误信息
                echo 'data: '.json_encode([
                    'type' => 'error',
                    'data' => $e->getMessage(),
                    'timestamp' => microtime(true),
                ])."\n\n";

                // 发送结束标记
                echo 'data: '.json_encode([
                    'type' => 'end',
                    'timestamp' => microtime(true),
                ])."\n\n";

                $this->flushOutput();
            }
        }, 200, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'X-Accel-Buffering' => 'no', // 禁用 Nginx 缓冲
        ]);
    }

    /**
     * 强制刷新输出缓冲区
     */
    protected function flushOutput(): void
    {
        if (ob_get_level()) {
            ob_flush();
        }
        flush();
    }
}
