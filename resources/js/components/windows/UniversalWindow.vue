<template>
    <div
        v-if="window.isVisible"
        v-show="!window.isMinimized"
        ref="windowElement"
        class="fixed overflow-hidden rounded-lg border border-border bg-background shadow-2xl"
        :class="{
            'ring-2 ring-primary/20': isActive,
            'shadow-3xl': isActive,
        }"
        :style="windowStyles"
        @mousedown="handleWindowClick"
    >
        <!-- 窗口标题栏 -->
        <div
            ref="titleBar"
            :class="[
                'flex items-center justify-between border-b border-border px-4 py-2 select-none',
                window.isDraggable ? 'cursor-move' : '',
                isActive ? 'bg-muted/50' : 'bg-muted/30',
            ]"
            @mousedown="startDrag"
            @dblclick="handleTitleBarDoubleClick"
        >
            <!-- 标题区域 -->
            <div class="flex min-w-0 flex-1 items-center gap-2">
                <!-- 图标 -->
                <component :is="getIcon(window.icon || window.type)" class="h-4 w-4 flex-shrink-0" :class="getIconClass(window.type)" />

                <!-- 标题 -->
                <span class="truncate text-sm font-medium" :title="window.title">
                    {{ window.title }}
                </span>

                <!-- 状态指示器 -->
                <div v-if="showStatus" class="flex flex-shrink-0 items-center gap-1">
                    <div :class="['h-2 w-2 rounded-full', getStatusClass()]" />
                    <span class="text-xs whitespace-nowrap text-muted-foreground">
                        {{ getStatusText() }}
                    </span>
                </div>
            </div>

            <!-- 窗口控制按钮 -->
            <div class="flex flex-shrink-0 items-center gap-1">
                <Button variant="ghost" size="sm" @click="$emit('minimize', window.id)" class="h-6 w-6 p-0 hover:bg-muted" title="最小化">
                    <Minus class="h-3 w-3" />
                </Button>

                <Button
                    v-if="window.isResizable"
                    variant="ghost"
                    size="sm"
                    @click="handleMaximizeClick"
                    class="h-6 w-6 p-0 hover:bg-muted"
                    :title="window.isMaximized ? '还原' : '最大化'"
                >
                    <component :is="window.isMaximized ? Minimize2 : Maximize2" class="h-3 w-3" />
                </Button>

                <Button
                    variant="ghost"
                    size="sm"
                    @click="$emit('close', window.id)"
                    class="h-6 w-6 p-0 hover:bg-destructive hover:text-destructive-foreground"
                    title="关闭"
                >
                    <X class="h-3 w-3" />
                </Button>
            </div>
        </div>

        <!-- 窗口内容区域 -->
        <div class="window-content relative" :style="contentStyles">
            <!-- 交互遮罩层 -->
            <div v-if="isInteracting" class="absolute inset-0 z-20" />

            <!-- 终端窗口内容 -->
            <PodTerminal
                v-if="window.type === 'terminal'"
                :pod-name="window.data?.podName"
                :container-name="window.data?.containerName"
                :mode="window.data?.mode || 'shell'"
                :auto-connect="true"
                :show-status-bar="false"
                class="h-full w-full"
                @connection-status-changed="handleTerminalStatusChange"
                @pod-selected="handlePodSelected"
            />

            <!-- AI 助手窗口内容 -->
            <AIAssistantContent v-else-if="window.type === 'ai-assistant'" class="h-full w-full" @loading-change="handleAILoadingChange" />

            <!-- 文件管理器窗口内容 -->
            <div v-else-if="window.type === 'file-manager'" class="h-full w-full overflow-auto">
                <PodFileManager class="h-full" />
            </div>

            <!-- 浏览器窗口内容 -->
            <div v-else-if="window.type === 'browser' && window.data?.url" class="relative h-full w-full">
                <iframe
                    :src="window.data.url"
                    class="h-full w-full border-none"
                    sandbox="allow-same-origin allow-scripts allow-forms allow-popups"
                    @load="handleIframeLoad"
                />
                <div v-if="isIframeLoading" class="absolute inset-0 z-10 flex items-center justify-center bg-background/70 backdrop-blur-sm">
                    <span class="text-sm text-muted-foreground">加载中...</span>
                </div>
            </div>

            <!-- 自定义窗口内容 -->
            <div v-else class="flex h-full w-full items-center justify-center p-4">
                <div class="text-center text-muted-foreground">
                    <component :is="getIcon(window.type)" class="mx-auto mb-2 h-12 w-12" />
                    <p>{{ window.title }}</p>
                    <p class="text-sm">窗口类型: {{ window.type }}</p>
                </div>
            </div>
        </div>

        <!-- 调整大小手柄 -->
        <div
            v-if="window.isResizable && !window.isMaximized"
            ref="resizeHandle"
            class="absolute right-0 bottom-0 h-4 w-4 cursor-nw-resize opacity-50 hover:opacity-100"
            @mousedown="startResize"
        >
            <div class="absolute right-1 bottom-1 h-2 w-2 border-r border-b border-border" />
        </div>
    </div>
</template>

<script setup lang="ts">
import AIAssistantContent from '@/components/AIAssistant/AIAssistantContent.vue';
import PodFileManager from '@/components/PodFileManager.vue';
import PodTerminal from '@/components/PodTerminal.vue';
import { Button } from '@/components/ui/button';
import type { WindowInstance } from '@/types/window';
import { File, Folder, Globe, Maximize2, Minimize2, Minus, Sparkles, Terminal, X } from 'lucide-vue-next';
import { computed, onMounted, onUnmounted, ref } from 'vue';

interface Props {
    window: WindowInstance;
    isActive?: boolean;
}

interface Emits {
    close: [windowId: string];
    minimize: [windowId: string];
    maximize: [windowId: string];
    restore: [windowId: string];
    focus: [windowId: string];
    update: [payload: { id: string; updates: Partial<WindowInstance> }];
}

const props = withDefaults(defineProps<Props>(), {
    isActive: false,
});

const emit = defineEmits<Emits>();

// Refs
const windowElement = ref<HTMLElement>();
const titleBar = ref<HTMLElement>();
const resizeHandle = ref<HTMLElement>();

// 浏览器窗口加载状态
const isIframeLoading = ref(false);

const handleIframeLoad = () => {
    isIframeLoading.value = false;
};

// 当窗口初始化且类型为 browser 时，显示加载蒙版
if (props.window.type === 'browser') {
    isIframeLoading.value = true;
}

// 状态
const isDragging = ref(false);
const isResizing = ref(false);
const dragOffset = ref({ x: 0, y: 0 });
const resizeStart = ref({ x: 0, y: 0, width: 0, height: 0 });
const originalSize = ref({ ...props.window.size });

const isInteracting = computed(() => isDragging.value || isResizing.value);

// 计算属性
const windowStyles = computed(() => {
    const styles: Record<string, string> = {
        left: `${props.window.position.x}px`,
        top: `${props.window.position.y}px`,
        zIndex: props.window.zIndex.toString(),
    };

    if (props.window.isMaximized) {
        styles.left = '0px';
        styles.top = '0px';
        styles.width = '100vw';
        styles.height = '100vh';
    } else {
        styles.width = `${props.window.size.width}px`;
        styles.height = `${props.window.size.height}px`;
    }

    return styles;
});

const contentStyles = computed(() => {
    const headerHeight = 41; // 标题栏高度
    return {
        height: props.window.isMaximized ? `calc(100vh - ${headerHeight}px)` : `${props.window.size.height - headerHeight}px`,
    };
});

const showStatus = computed(() => {
    return props.window.type === 'terminal' && props.window.data?.isConnected !== undefined;
});

// 图标映射
const getIcon = (iconName: string) => {
    const iconMap: Record<string, any> = {
        terminal: Terminal,
        'ai-assistant': Sparkles,
        'file-manager': Folder,
        editor: File,
        browser: Globe,
        Terminal: Terminal,
        Sparkles: Sparkles,
        Folder: Folder,
        File: File,
        Globe: Globe,
    };
    return iconMap[iconName] || File;
};

const getIconClass = (type: string) => {
    const classMap: Record<string, string> = {
        terminal: 'text-green-500',
        'ai-assistant': 'text-primary',
        'file-manager': 'text-blue-500',
        editor: 'text-orange-500',
        browser: 'text-purple-500',
    };
    return classMap[type] || 'text-muted-foreground';
};

const getStatusClass = () => {
    if (props.window.type === 'terminal') {
        return props.window.data?.isConnected ? 'bg-green-500' : 'bg-red-500';
    }
    return 'bg-gray-500';
};

const getStatusText = () => {
    if (props.window.type === 'terminal') {
        return props.window.data?.isConnected ? '已连接' : '未连接';
    }
    return '';
};

// 窗口控制
const handleWindowClick = () => {
    emit('focus', props.window.id);
};

const handleTitleBarDoubleClick = () => {
    if (props.window.isResizable) {
        handleMaximizeClick();
    }
};

const handleMaximizeClick = () => {
    if (props.window.isMaximized) {
        emit('restore', props.window.id);
    } else {
        emit('maximize', props.window.id);
    }
};

// 拖拽功能
const startDrag = (event: MouseEvent) => {
    if (!props.window.isDraggable || props.window.isMaximized) return;

    isDragging.value = true;
    dragOffset.value = {
        x: event.clientX - props.window.position.x,
        y: event.clientY - props.window.position.y,
    };

    document.addEventListener('mousemove', handleDrag);
    document.addEventListener('mouseup', stopDrag, { once: true });
    document.body.style.userSelect = 'none';
    event.preventDefault();
};

const handleDrag = (event: MouseEvent) => {
    if (!isDragging.value) return;

    const safetyMargin = 60; // The minimum part of the window (in px) that should stay visible
    const titleBarHeight = titleBar.value?.clientHeight || 41;

    const newX = event.clientX - dragOffset.value.x;
    const newY = event.clientY - dragOffset.value.y;

    const minX = -(props.window.size.width - safetyMargin);
    const maxX = window.innerWidth - safetyMargin;

    // Allow moving off-screen, but keep a small part of the title bar visible
    const minY = -(titleBarHeight - 10);
    const maxY = window.innerHeight - safetyMargin;

    const newPosition = {
        x: Math.max(minX, Math.min(maxX, newX)),
        y: Math.max(minY, Math.min(maxY, newY)),
    };

    emit('update', {
        id: props.window.id,
        updates: { position: newPosition },
    });
};

const stopDrag = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', handleDrag);
    document.body.style.userSelect = '';
};

// 调整大小功能
const startResize = (event: MouseEvent) => {
    if (!props.window.isResizable) return;

    isResizing.value = true;
    resizeStart.value = {
        x: event.clientX,
        y: event.clientY,
        width: props.window.size.width,
        height: props.window.size.height,
    };

    document.addEventListener('mousemove', handleResize);
    document.addEventListener('mouseup', stopResize, { once: true });
    document.body.style.userSelect = 'none';
    event.preventDefault();
    event.stopPropagation();
};

const handleResize = (event: MouseEvent) => {
    if (!isResizing.value) return;

    const deltaX = event.clientX - resizeStart.value.x;
    const deltaY = event.clientY - resizeStart.value.y;

    const minSize = props.window.minSize || { width: 300, height: 200 };
    const maxSize = props.window.maxSize || {
        width: window.innerWidth,
        height: window.innerHeight,
    };

    const newWidth = Math.max(minSize.width, Math.min(maxSize.width, resizeStart.value.width + deltaX));

    const newHeight = Math.max(minSize.height, Math.min(maxSize.height, resizeStart.value.height + deltaY));

    emit('update', {
        id: props.window.id,
        updates: { size: { width: newWidth, height: newHeight } },
    });
};

const stopResize = () => {
    isResizing.value = false;
    document.removeEventListener('mousemove', handleResize);
    document.body.style.userSelect = '';
};

// 内容事件处理
const handleTerminalStatusChange = (status: string) => {
    emit('update', {
        id: props.window.id,
        updates: {
            data: {
                ...props.window.data,
                isConnected: status === 'connected',
            },
        },
    });
};

const handlePodSelected = (pod: any, container: string) => {
    // 更新窗口标题和数据
    emit('update', {
        id: props.window.id,
        updates: {
            title: `终端 - ${pod.name} (${container})`,
            data: {
                ...props.window.data,
                podName: pod.name,
                containerName: container,
                isConnected: false,
            },
        },
    });
};

const handleAILoadingChange = (isLoading: boolean) => {
    emit('update', {
        id: props.window.id,
        updates: {
            data: {
                ...props.window.data,
                isLoading,
            },
        },
    });
};

// 生命周期
onMounted(() => {
    // 保存原始大小
    originalSize.value = { ...props.window.size };
});

onUnmounted(() => {
    // 清理事件监听器
    document.removeEventListener('mousemove', handleDrag);
    document.removeEventListener('mouseup', stopDrag);
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', stopResize);
    document.body.style.userSelect = '';
});
</script>

<style scoped>
.window-content {
    overflow: hidden;
}

.cursor-move {
    cursor: move;
}

.cursor-nw-resize {
    cursor: nw-resize;
}

/* 活动窗口的特殊样式 */
.shadow-3xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}
</style>
