<template>
    <div class="space-y-6">
        <!-- Pod 和容器选择 -->
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div class="space-y-2">
                <Label>选择 Pod</Label>
                <Select v-model="selectedPod" @update:modelValue="handlePodChange">
                    <SelectTrigger>
                        <SelectValue placeholder="请选择 Pod" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem v-for="pod in pods" :key="pod.name" :value="pod.name">
                            {{ pod.name }}
                        </SelectItem>
                    </SelectContent>
                </Select>
            </div>

            <div class="space-y-2">
                <Label>选择容器</Label>
                <Select v-model="selectedContainer" @update:modelValue="handleContainerChange" :disabled="!selectedPod">
                    <SelectTrigger>
                        <SelectValue placeholder="请选择容器" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem v-for="container in currentPodContainers" :key="container.name" :value="container.name">
                            {{ container.name }}
                        </SelectItem>
                    </SelectContent>
                </Select>
            </div>
        </div>

        <!-- 文件管理器主体 -->
        <div v-if="selectedPod && selectedContainer" class="space-y-4">
            <!-- 路径导航和操作按钮 -->
            <div class="flex items-center justify-between rounded-lg border p-3">
                <div class="flex items-center gap-2">
                    <Folder class="h-4 w-4 text-gray-500" />
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem v-for="(segment, index) in pathSegments" :key="index">
                                <BreadcrumbLink @click="navigateToPath(getPathFromSegments(index))" class="cursor-pointer hover:text-blue-600">
                                    {{ index === 0 ? '根目录' : segment }}
                                </BreadcrumbLink>
                                <BreadcrumbSeparator v-if="index < pathSegments.length - 1" />
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                </div>

                <div class="flex items-center gap-2">
                    <Button variant="outline" size="sm" @click="refreshFileList" :disabled="loading">
                        <RefreshCw :class="['h-4 w-4', { 'animate-spin': loading }]" />
                    </Button>
                    <Button variant="outline" size="sm" @click="showCreateDialog = true">
                        <Plus class="mr-2 h-4 w-4" />
                        新建
                    </Button>
                    <Button variant="outline" size="sm" @click="showUploadDialog = true">
                        <Upload class="mr-2 h-4 w-4" />
                        上传
                    </Button>
                </div>
            </div>

            <!-- 批量操作工具栏 -->
            <div
                v-if="selectedFiles.length > 0"
                class="flex items-center justify-between rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-900/20"
            >
                <div class="flex items-center gap-3">
                    <CheckSquare class="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <span class="text-sm font-medium text-blue-900 dark:text-blue-100">已选择 {{ selectedFiles.length }} 项</span>
                    <Button
                        variant="ghost"
                        size="sm"
                        @click="clearSelection"
                        class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                        清除选择
                    </Button>
                </div>
                <div class="flex items-center gap-2">
                    <Button variant="outline" size="sm" @click="downloadSelected">
                        <Download class="mr-2 h-4 w-4" />
                        下载
                    </Button>
                    <Button variant="outline" size="sm" @click="compressSelected">
                        <Archive class="mr-2 h-4 w-4" />
                        压缩
                    </Button>
                    <Button variant="destructive" size="sm" @click="deleteSelected">
                        <Trash2 class="mr-2 h-4 w-4" />
                        删除
                    </Button>
                </div>
            </div>

            <!-- 文件列表 -->
            <div class="space-y-2">
                <div v-if="loading" class="py-8 text-center">
                    <Loader2 class="mx-auto h-8 w-8 animate-spin text-gray-400" />
                    <p class="mt-2 text-gray-500">加载中...</p>
                </div>

                <div v-else-if="files.length === 0" class="py-8 text-center">
                    <FolderOpen class="mx-auto h-12 w-12 text-gray-300 dark:text-gray-600" />
                    <p class="mt-2 text-gray-500">此目录为空</p>
                </div>

                <div v-else class="space-y-1">
                    <!-- 全选复选框 -->
                    <div class="mb-2 flex items-center gap-3 px-3">
                        <input
                            type="checkbox"
                            :checked="selectedFiles.length === files.length && files.length > 0"
                            @change="toggleSelectAll"
                            class="h-4 w-4 rounded border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                        />
                        <span class="text-sm text-gray-600 dark:text-gray-400">全选</span>
                    </div>

                    <!-- 文件项 -->
                    <div
                        v-for="file in files"
                        :key="file.name"
                        class="group flex items-center gap-3 rounded-md border p-3 transition-all duration-200 hover:shadow-sm"
                        :class="{
                            'border-blue-200 bg-blue-50 dark:border-blue-700 dark:bg-blue-900/30': selectedFiles.includes(file.name),
                            'border-gray-200 hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-700':
                                !selectedFiles.includes(file.name),
                        }"
                    >
                        <!-- 选择复选框 -->
                        <input
                            type="checkbox"
                            :checked="selectedFiles.includes(file.name)"
                            @change.stop="toggleFileSelection(file.name)"
                            class="h-4 w-4 rounded border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                        />

                        <!-- 文件图标 -->
                        <component :is="getFileIconComponent(file)" :class="['h-5 w-5', getFileIconColor(file)]" />

                        <!-- 文件信息 - 可点击区域 -->
                        <div class="min-w-0 flex-1 cursor-pointer" @click="handleFileClick(file)">
                            <div class="flex items-center gap-2">
                                <span class="truncate text-sm font-medium text-gray-900 dark:text-gray-100">{{ file.name }}</span>
                                <span v-if="file.linkTarget" class="truncate text-xs text-gray-500 dark:text-gray-400">→ {{ file.linkTarget }}</span>
                            </div>
                            <div class="mt-1 flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                                <span>{{ formatFileSize(file.size) }}</span>
                                <span>{{ file.permissions }}</span>
                                <span v-if="file.owner">{{ file.owner }}</span>
                                <span>{{ file.modified }}</span>
                            </div>
                        </div>

                        <!-- 操作菜单 -->
                        <div class="flex-shrink-0 opacity-0 transition-opacity group-hover:opacity-100">
                            <DropdownMenu>
                                <DropdownMenuTrigger as-child>
                                    <Button variant="ghost" size="sm" class="h-8 w-8 p-0" @click.stop>
                                        <MoreHorizontal class="h-4 w-4" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" class="w-56">
                                    <DropdownMenuItem v-if="file.type === 'directory'" @click.stop="navigateToFile(file)">
                                        <FolderOpen class="mr-2 h-4 w-4" />
                                        打开目录
                                    </DropdownMenuItem>
                                    <DropdownMenuItem v-if="file.type === 'file' && isTextFile(file.name)" @click.stop="previewFile(file)">
                                        <Eye class="mr-2 h-4 w-4" />
                                        预览
                                    </DropdownMenuItem>
                                    <DropdownMenuItem v-if="file.type === 'file' && isTextFile(file.name)" @click.stop="editFile(file)">
                                        <Edit class="mr-2 h-4 w-4" />
                                        编辑
                                    </DropdownMenuItem>
                                    <DropdownMenuItem v-if="file.type === 'file'" @click.stop="downloadFile(file)">
                                        <Download class="mr-2 h-4 w-4" />
                                        下载
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem v-if="isArchiveFile(file.name)" @click.stop="extractFile(file)">
                                        <Archive class="mr-2 h-4 w-4" />
                                        解压缩
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem @click.stop="deleteFile(file)" class="text-red-600 dark:text-red-400">
                                        <Trash2 class="mr-2 h-4 w-4" />
                                        删除
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文件预览对话框 -->
        <Dialog :open="showPreviewDialog" @update:open="showPreviewDialog = $event">
            <DialogContent class="max-h-[80vh] max-w-4xl">
                <DialogHeader>
                    <DialogTitle class="flex items-center gap-2">
                        <Eye class="h-5 w-5" />
                        预览文件: {{ currentFile?.name }}
                    </DialogTitle>
                </DialogHeader>
                <div class="mt-4 max-h-96 overflow-auto">
                    <div v-if="loadingContent" class="py-8 text-center">
                        <Loader2 class="mx-auto h-6 w-6 animate-spin text-gray-400" />
                        <p class="mt-2 text-gray-500">加载中...</p>
                    </div>
                    <pre v-else class="overflow-x-auto rounded border bg-gray-50 p-4 font-mono text-sm whitespace-pre-wrap dark:bg-gray-800">{{
                        fileContent
                    }}</pre>
                </div>
            </DialogContent>
        </Dialog>

        <!-- 文件编辑对话框 -->
        <Dialog :open="showEditDialog" @update:open="showEditDialog = $event">
            <DialogContent class="max-h-[80vh] max-w-4xl">
                <DialogHeader>
                    <DialogTitle class="flex items-center gap-2">
                        <Edit class="h-5 w-5" />
                        编辑文件: {{ currentFile?.name }}
                    </DialogTitle>
                </DialogHeader>
                <div class="mt-4 space-y-4">
                    <div v-if="loadingContent" class="py-8 text-center">
                        <Loader2 class="mx-auto h-6 w-6 animate-spin text-gray-400" />
                        <p class="mt-2 text-gray-500">加载中...</p>
                    </div>
                    <div v-else class="space-y-4">
                        <Textarea v-model="fileContent" placeholder="文件内容" class="min-h-64 font-mono text-sm" />
                        <div class="flex gap-2">
                            <Button @click="saveFile" :disabled="loadingContent">
                                <Save class="mr-2 h-4 w-4" />
                                保存
                            </Button>
                            <Button variant="outline" @click="showEditDialog = false">取消</Button>
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>

        <!-- 创建对话框 -->
        <Dialog :open="showCreateDialog" @update:open="showCreateDialog = $event">
            <DialogContent>
                <DialogHeader>
                    <DialogTitle class="flex items-center gap-2">
                        <Plus class="h-5 w-5" />
                        创建文件或目录
                    </DialogTitle>
                </DialogHeader>
                <div class="mt-4 space-y-4">
                    <div>
                        <Label>类型</Label>
                        <Select v-model="createType">
                            <SelectTrigger>
                                <SelectValue placeholder="选择类型" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="file">文件</SelectItem>
                                <SelectItem value="directory">目录</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Label>名称</Label>
                        <Input v-model="createName" placeholder="输入名称" @keyup.enter="createItem" />
                    </div>
                    <div class="flex gap-2">
                        <Button @click="createItem" :disabled="!createType || !createName">创建</Button>
                        <Button variant="outline" @click="showCreateDialog = false">取消</Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>

        <!-- 上传对话框 -->
        <Dialog :open="showUploadDialog" @update:open="showUploadDialog = $event">
            <DialogContent>
                <DialogHeader>
                    <DialogTitle class="flex items-center gap-2">
                        <Upload class="h-5 w-5" />
                        上传文件
                    </DialogTitle>
                </DialogHeader>
                <div class="mt-4 space-y-4">
                    <div class="rounded-lg border-2 border-dashed border-gray-300 p-6 text-center dark:border-gray-600">
                        <UploadCloud class="mx-auto mb-4 h-12 w-12 text-gray-400" />
                        <input type="file" ref="fileInput" @change="handleFileSelect" multiple class="hidden" />
                        <Button @click="fileInput?.click()">选择文件</Button>
                        <p class="mt-2 text-sm text-gray-500">最大文件大小: 128KB</p>
                    </div>
                    <div v-if="uploadFileList.length > 0" class="space-y-2">
                        <div
                            v-for="(file, index) in uploadFileList"
                            :key="index"
                            class="flex items-center justify-between rounded bg-gray-50 p-2 dark:bg-gray-800"
                        >
                            <span class="truncate text-sm">{{ file.name }}</span>
                            <Button variant="ghost" size="sm" @click="removeUploadFile(index)">
                                <X class="h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <Button @click="uploadSelectedFiles" :disabled="uploadFileList.length === 0">
                            <Upload class="mr-2 h-4 w-4" />
                            上传
                        </Button>
                        <Button variant="outline" @click="showUploadDialog = false">取消</Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>

        <!-- 删除文件确认对话框 -->
        <AlertDialog :open="showDeleteDialog" @update:open="showDeleteDialog = $event">
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>确认删除</AlertDialogTitle>
                    <AlertDialogDescription>
                        确认删除 <strong>{{ fileToDelete?.name }}</strong>？此操作不可撤销。
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel @click="showDeleteDialog = false">取消</AlertDialogCancel>
                    <AlertDialogAction @click="confirmDeleteFile" class="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                        删除
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>

        <!-- 批量删除确认对话框 -->
        <AlertDialog :open="showDeleteSelectedDialog" @update:open="showDeleteSelectedDialog = $event">
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>确认批量删除</AlertDialogTitle>
                    <AlertDialogDescription>
                        确认删除 <strong>{{ selectedFiles.length }}</strong> 个项目？此操作不可撤销。
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel @click="showDeleteSelectedDialog = false">取消</AlertDialogCancel>
                    <AlertDialogAction @click="confirmDeleteSelected" class="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                        删除
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    </div>
</template>

<script setup lang="ts">
import axios from '@/lib/axios';
import { useResourcesStore } from '@/stores/resourcesStore';
import { computed, onMounted, ref, watch, withDefaults } from 'vue';
import { toast } from 'vue-sonner';

// ShadcnUI Components
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

// Lucide Icons
import {
    Archive,
    CheckSquare,
    Download,
    Edit,
    Eye,
    File,
    Folder,
    FolderOpen,
    Link,
    Loader2,
    MoreHorizontal,
    Plus,
    RefreshCw,
    Save,
    Trash2,
    Upload,
    UploadCloud,
    X,
} from 'lucide-vue-next';

interface Pod {
    name: string;
    containers: Array<{ name: string }>;
}

interface FileItem {
    name: string;
    type: 'file' | 'directory' | 'link' | 'block' | 'character' | 'pipe' | 'socket';
    size: number;
    permissions: string;
    modified: string;
    owner?: string;
    group?: string;
    isExecutable?: boolean;
    linkTarget?: string;
}

// Props (keeping for backward compatibility, but will use store)
interface Props {
    pods?: Pod[];
}

withDefaults(defineProps<Props>(), {
    pods: () => [],
});

// Use resources store to get pods reactively
const resourcesStore = useResourcesStore();

// Get pods from store instead of props
const pods = computed(() => {
    return resourcesStore.collections.pods.map((pod) => ({
        name: pod.name,
        containers: pod.containers || [],
    }));
});

// 状态
const selectedPod = ref('');
const selectedContainer = ref('');
const currentPath = ref('/');
const files = ref<FileItem[]>([]);
const selectedFiles = ref<string[]>([]);
const loading = ref(false);

// 对话框状态
const showPreviewDialog = ref(false);
const showEditDialog = ref(false);
const showCreateDialog = ref(false);
const showUploadDialog = ref(false);
const showDeleteDialog = ref(false);
const showDeleteSelectedDialog = ref(false);
const currentFile = ref<FileItem | null>(null);
const fileContent = ref('');
const loadingContent = ref(false);
const fileToDelete = ref<FileItem | null>(null);

// 创建和上传
const createType = ref('');
const createName = ref('');
const uploadFileList = ref<File[]>([]);
const fileInput = ref<HTMLInputElement>();

// 计算属性
const currentPodContainers = computed(() => {
    if (!selectedPod.value) return [];
    const pod = pods.value.find((p: Pod) => p.name === selectedPod.value);
    return pod?.containers || [];
});

const pathSegments = computed(() => {
    return currentPath.value.split('/').filter((segment, index) => index === 0 || segment !== '');
});

// 工具函数
const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

const getFileIconComponent = (file: FileItem) => {
    if (file.type === 'directory') return Folder;
    if (file.type === 'link') return Link;
    return File;
};

const getFileIconColor = (file: FileItem) => {
    if (file.type === 'directory') return 'text-blue-500';
    if (file.type === 'link') return 'text-green-500';
    if (file.isExecutable) return 'text-orange-500';
    return 'text-gray-500 dark:text-gray-400';
};

const isTextFile = (fileName: string): boolean => {
    const textExtensions = ['txt', 'md', 'json', 'js', 'ts', 'html', 'css', 'xml', 'yaml', 'yml', 'sh', 'py', 'php', 'log', 'conf'];
    const ext = fileName.split('.').pop()?.toLowerCase();
    return textExtensions.includes(ext || '');
};

const isArchiveFile = (fileName: string): boolean => {
    const archiveExtensions = ['zip', 'tar', 'gz', 'bz2', 'xz', 'rar', '7z'];
    return archiveExtensions.some((ext) => fileName.toLowerCase().endsWith(ext));
};

// 核心方法
const executeCommand = async (command: string[]) => {
    try {
        const response = await axios.post(`/api/pods/${selectedPod.value}/exec`, {
            command,
            container: selectedContainer.value,
            timeout: 30,
        });

        const result = response.data;
        return {
            success: result.success || result.exit_code === 0 || (result.completed && !result.error),
            stdout: result.stdout || '',
            stderr: result.stderr || '',
            error: result.error,
        };
    } catch (error: any) {
        return {
            success: false,
            stdout: '',
            stderr: '',
            error: error.response?.data?.message || error.message,
        };
    }
};

const parseLsOutput = (output: string): FileItem[] => {
    const lines = output.split('\n').filter((line) => line.trim());
    const items: FileItem[] = [];

    for (const line of lines) {
        if (line.startsWith('total ') || line.trim() === '') continue;

        try {
            const parts = line.split(/\s+/);
            if (parts.length < 9) continue;

            const permissions = parts[0];
            const owner = parts[2];
            const group = parts[3];
            const size = parseInt(parts[4]) || 0;
            const dateTime = `${parts[5]} ${parts[6]}`;
            const name = parts.slice(8).join(' ');

            if (name === '.' || name === '..') continue;

            let type: FileItem['type'] = 'file';
            let linkTarget: string | undefined;

            if (permissions.startsWith('d')) {
                type = 'directory';
            } else if (permissions.startsWith('l')) {
                type = 'link';
                const linkMatch = name.match(/^(.+) -> (.+)$/);
                if (linkMatch) {
                    linkTarget = linkMatch[2];
                }
            }

            items.push({
                name: linkTarget ? name.split(' -> ')[0] : name,
                type,
                size,
                permissions,
                modified: dateTime,
                owner,
                group,
                isExecutable: permissions.includes('x'),
                linkTarget,
            });
        } catch (error) {
            console.warn('Failed to parse line:', line);
        }
    }

    return items.sort((a, b) => {
        if (a.type === 'directory' && b.type !== 'directory') return -1;
        if (a.type !== 'directory' && b.type === 'directory') return 1;
        return a.name.localeCompare(b.name);
    });
};

// 事件处理
const handlePodChange = (value: any) => {
    const podName = value as string;
    if (!podName) return;
    selectedPod.value = podName;
    selectedContainer.value = '';
    currentPath.value = '/';
    files.value = [];
    selectedFiles.value = [];

    const pod = pods.value.find((p: Pod) => p.name === podName);
    if (pod && pod.containers.length > 0) {
        selectedContainer.value = pod.containers[0].name;
        refreshFileList();
    }
};

const handleContainerChange = (value: any) => {
    const containerName = value as string;
    if (!containerName) return;
    selectedContainer.value = containerName;
    currentPath.value = '/';
    files.value = [];
    selectedFiles.value = [];
    if (containerName) {
        refreshFileList();
    }
};

const refreshFileList = async () => {
    if (!selectedPod.value || !selectedContainer.value) return;

    loading.value = true;
    try {
        const response = await executeCommand(['ls', '-la', '--time-style=full-iso', currentPath.value]);

        if (response.success) {
            files.value = parseLsOutput(response.stdout);
        } else {
            toast.error(`无法列出文件: ${response.stderr || response.error}`);
        }
    } catch (error: any) {
        toast.error(`获取文件列表失败: ${error.message}`);
    } finally {
        loading.value = false;
    }
};

const navigateToPath = (path: string) => {
    currentPath.value = path;
    selectedFiles.value = [];
    refreshFileList();
};

const getPathFromSegments = (index: number): string => {
    if (index === 0) return '/';
    const segments = pathSegments.value.slice(1, index + 1);
    return '/' + segments.join('/');
};

const handleFileClick = (file: FileItem) => {
    if (file.type === 'directory') {
        navigateToFile(file);
    }
    // 对于文件，不在这里处理选择，选择由复选框单独处理
};

const navigateToFile = (file: FileItem) => {
    if (file.type === 'directory') {
        const newPath = currentPath.value === '/' ? `/${file.name}` : `${currentPath.value}/${file.name}`;
        navigateToPath(newPath);
    }
};

const toggleFileSelection = (fileName: string) => {
    const index = selectedFiles.value.indexOf(fileName);
    if (index === -1) {
        selectedFiles.value.push(fileName);
    } else {
        selectedFiles.value.splice(index, 1);
    }
};

const toggleSelectAll = () => {
    if (selectedFiles.value.length === files.value.length && files.value.length > 0) {
        selectedFiles.value = [];
    } else {
        selectedFiles.value = files.value.map((f) => f.name);
    }
};

const clearSelection = () => {
    selectedFiles.value = [];
};

// 文件操作
const previewFile = async (file: FileItem) => {
    if (!isTextFile(file.name)) {
        toast.error('此文件类型不支持预览');
        return;
    }

    currentFile.value = file;
    loadingContent.value = true;
    showPreviewDialog.value = true;

    try {
        const filePath = currentPath.value === '/' ? `/${file.name}` : `${currentPath.value}/${file.name}`;
        const result = await executeCommand(['cat', filePath]);

        if (result.success) {
            fileContent.value = result.stdout;
        } else {
            toast.error(`预览文件失败: ${result.stderr || result.error}`);
            showPreviewDialog.value = false;
        }
    } catch (error: any) {
        toast.error(`预览文件失败: ${error.message}`);
        showPreviewDialog.value = false;
    } finally {
        loadingContent.value = false;
    }
};

const editFile = async (file: FileItem) => {
    if (!isTextFile(file.name)) {
        toast.error('此文件类型不支持编辑');
        return;
    }

    currentFile.value = file;
    loadingContent.value = true;
    showEditDialog.value = true;

    try {
        const filePath = currentPath.value === '/' ? `/${file.name}` : `${currentPath.value}/${file.name}`;
        const result = await executeCommand(['cat', filePath]);

        if (result.success) {
            fileContent.value = result.stdout;
        } else {
            toast.error(`读取文件失败: ${result.stderr || result.error}`);
            showEditDialog.value = false;
        }
    } catch (error: any) {
        toast.error(`读取文件失败: ${error.message}`);
        showEditDialog.value = false;
    } finally {
        loadingContent.value = false;
    }
};

const saveFile = async () => {
    if (!currentFile.value) return;

    try {
        const filePath = currentPath.value === '/' ? `/${currentFile.value.name}` : `${currentPath.value}/${currentFile.value.name}`;
        const encodedContent = btoa(fileContent.value);
        const result = await executeCommand(['bash', '-c', `echo '${encodedContent}' | base64 -d > '${filePath.replace(/'/g, "'\"'\"'")}'`]);

        if (result.success) {
            toast.success('文件保存成功');
            showEditDialog.value = false;
            refreshFileList();
        } else {
            toast.error(`保存文件失败: ${result.stderr || result.error}`);
        }
    } catch (error: any) {
        toast.error(`保存文件失败: ${error.message}`);
    }
};

const downloadFile = async (file: FileItem) => {
    try {
        const filePath = currentPath.value === '/' ? `/${file.name}` : `${currentPath.value}/${file.name}`;
        const result = await executeCommand(['base64', '-w', '0', filePath]);

        if (result.success) {
            const binaryString = atob(result.stdout);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            const blob = new Blob([bytes]);
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = file.name;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            toast.success('文件下载成功');
        } else {
            toast.error(`下载文件失败: ${result.stderr || result.error}`);
        }
    } catch (error: any) {
        toast.error(`下载文件失败: ${error.message}`);
    }
};

const deleteFile = (file: FileItem) => {
    fileToDelete.value = file;
    showDeleteDialog.value = true;
};

const confirmDeleteFile = async () => {
    if (!fileToDelete.value) return;

    try {
        const filePath = currentPath.value === '/' ? `/${fileToDelete.value.name}` : `${currentPath.value}/${fileToDelete.value.name}`;
        const command = fileToDelete.value.type === 'directory' ? ['rm', '-rf', filePath] : ['rm', '-f', filePath];
        const result = await executeCommand(command);

        if (result.success) {
            toast.success('删除成功');
            refreshFileList();
        } else {
            toast.error(`删除失败: ${result.stderr || result.error}`);
        }
    } catch (error: any) {
        toast.error(`删除失败: ${error.message}`);
    } finally {
        showDeleteDialog.value = false;
        fileToDelete.value = null;
    }
};

const createItem = async () => {
    if (!createType.value || !createName.value) return;

    try {
        const itemPath = currentPath.value === '/' ? `/${createName.value}` : `${currentPath.value}/${createName.value}`;
        const command = createType.value === 'directory' ? ['mkdir', '-p', itemPath] : ['touch', itemPath];
        const result = await executeCommand(command);

        if (result.success) {
            toast.success(`${createType.value === 'directory' ? '目录' : '文件'}创建成功`);
            showCreateDialog.value = false;
            createType.value = '';
            createName.value = '';
            refreshFileList();
        } else {
            toast.error(`创建失败: ${result.stderr || result.error}`);
        }
    } catch (error: any) {
        toast.error(`创建失败: ${error.message}`);
    }
};

// 上传相关
const handleFileSelect = (event: Event) => {
    const input = event.target as HTMLInputElement;
    if (input.files) {
        uploadFileList.value = [...uploadFileList.value, ...Array.from(input.files)];
    }
};

const removeUploadFile = (index: number) => {
    uploadFileList.value.splice(index, 1);
};

const uploadSelectedFiles = async () => {
    for (const file of uploadFileList.value) {
        try {
            if (file.size > 128 * 1024) {
                toast.error(`文件 ${file.name} 超过 128KB 限制`);
                continue;
            }

            const reader = new FileReader();
            const fileContent = await new Promise<string>((resolve, reject) => {
                reader.onload = () => resolve(reader.result as string);
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });

            const base64Data = fileContent.split(',')[1];
            const filePath = currentPath.value === '/' ? `/${file.name}` : `${currentPath.value}/${file.name}`;
            const result = await executeCommand(['bash', '-c', `echo '${base64Data}' | base64 -d > '${filePath.replace(/'/g, "'\"'\"'")}'`]);

            if (result.success) {
                toast.success(`文件 ${file.name} 上传成功`);
            } else {
                toast.error(`上传 ${file.name} 失败: ${result.stderr || result.error}`);
            }
        } catch (error: any) {
            toast.error(`上传 ${file.name} 失败: ${error.message}`);
        }
    }

    showUploadDialog.value = false;
    uploadFileList.value = [];
    refreshFileList();
};

// 批量操作
const downloadSelected = () => {
    selectedFiles.value.forEach((fileName) => {
        const file = files.value.find((f) => f.name === fileName);
        if (file) downloadFile(file);
    });
};

const compressSelected = async () => {
    if (selectedFiles.value.length === 0) return;

    try {
        const result = await executeCommand(['tar', '-czf', 'selected_files.tar.gz', ...selectedFiles.value]);
        if (result.success) {
            toast.success('压缩完成');
            refreshFileList();
        } else {
            toast.error(`压缩失败: ${result.stderr || result.error}`);
        }
    } catch (error: any) {
        toast.error(`压缩失败: ${error.message}`);
    }
};

const deleteSelected = () => {
    if (selectedFiles.value.length === 0) return;
    showDeleteSelectedDialog.value = true;
};

const confirmDeleteSelected = async () => {
    if (selectedFiles.value.length === 0) return;

    for (const fileName of selectedFiles.value) {
        try {
            const filePath = currentPath.value === '/' ? `/${fileName}` : `${currentPath.value}/${fileName}`;
            await executeCommand(['rm', '-rf', filePath]);
        } catch (error: any) {
            toast.error(`删除 ${fileName} 失败: ${error.message}`);
        }
    }

    toast.success('批量删除完成');
    selectedFiles.value = [];
    showDeleteSelectedDialog.value = false;
    refreshFileList();
};

const extractFile = async (file: FileItem) => {
    try {
        const filePath = currentPath.value === '/' ? `/${file.name}` : `${currentPath.value}/${file.name}`;
        let command: string[];

        if (file.name.endsWith('.zip')) {
            command = ['unzip', filePath, '-d', currentPath.value];
        } else if (file.name.endsWith('.tar.gz') || file.name.endsWith('.tgz')) {
            command = ['tar', '-xzf', filePath, '-C', currentPath.value];
        } else {
            command = ['tar', '-xf', filePath, '-C', currentPath.value];
        }

        const result = await executeCommand(command);
        if (result.success) {
            toast.success('解压成功');
            refreshFileList();
        } else {
            toast.error(`解压失败: ${result.stderr || result.error}`);
        }
    } catch (error: any) {
        toast.error(`解压失败: ${error.message}`);
    }
};

onMounted(() => {
    // 等待 pods 从 store 加载完成后再自动选择
    let unwatch: (() => void) | null = null;

    unwatch = watch(
        pods,
        (newPods) => {
            if (!selectedPod.value && newPods.length > 0) {
                const firstPod = newPods[0];
                selectedPod.value = firstPod.name;

                if (firstPod.containers && firstPod.containers.length > 0) {
                    selectedContainer.value = firstPod.containers[0].name;
                    refreshFileList();
                }
                if (unwatch) {
                    unwatch(); // 只自动选择一次
                }
            }
        },
        { immediate: true }
    );
});
</script>
