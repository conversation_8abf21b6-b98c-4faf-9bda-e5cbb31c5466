<script setup lang="ts">
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import type { Auth, User, Workspace } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/vue3';
import { AlertCircle, ArrowLeft, Crown, Edit, Mail, Plus, Star, Trash2, UserMinus, Users } from 'lucide-vue-next';
import { toast } from 'sonner';
import { onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    workspace: Workspace;
    members: User[];
    invitations: any[];
    auth: Auth;
}

const props = defineProps<Props>();

// 提取实际的 workspace 数据
const workspace = props.workspace;

const isDeleting = ref(false);
const isSettingCurrent = ref(false);
const clusterPricing = ref<any>(null);
const loadingPricing = ref(false);
const showTransferDialog = ref(false);
const transferUserId = ref<number | null>(null);

// 邀请表单
const inviteForm = useForm({
    email: '',
    role: 'member',
});

const inviteUser = () => {
    inviteForm.post(route('invitations.store', { workspace: workspace.id }), {
        onSuccess: () => {
            inviteForm.reset();
            toast.success('邀请已发送');
        },
        onError: (errors) => {
            if (errors.email) {
                toast.error(errors.email);
            }
        },
    });
};

const cancelInvitation = (invitationId: number) => {
    router.delete(route('invitations.destroy', { workspace: workspace.id, invitation: invitationId }), {
        onSuccess: () => {
            toast.success('邀请已取消');
        },
    });
};

const removeMember = (userId: number) => {
    router.delete(route('workspace-members.destroy', { workspace: workspace.id, user: userId }), {
        onSuccess: () => {
            toast.success('成员已移除');
        },
    });
};

const transferOwnership = (userId: number) => {
    transferUserId.value = userId;
    showTransferDialog.value = true;
};

const confirmTransferOwnership = () => {
    if (!transferUserId.value) return;

    router.post(
        route('workspace-members.transfer-ownership', { workspace: workspace.id, user: transferUserId.value }),
        {},
        {
            onSuccess: () => {
                toast.success('所有权转让成功');
                showTransferDialog.value = false;
                transferUserId.value = null;
            },
        },
    );
};

const getStatusColor = (status: string) => {
    switch (status) {
        case 'active':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
        case 'pending':
        case 'creating':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
        case 'deleting':
            return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
        case 'suspended':
        case 'failed':
            return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
};

const getStatusText = (status: string) => {
    switch (status) {
        case 'active':
            return '正常';
        case 'pending':
            return '待激活';
        case 'creating':
            return '创建中';
        case 'deleting':
            return '删除中';
        case 'suspended':
            return '已暂停';
        case 'failed':
            return '创建失败';
        default:
            return status;
    }
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
};

const handleDelete = async () => {
    isDeleting.value = true;

    try {
        await axios.delete(`/api/workspaces/${workspace.id}`);
        toast.success('工作空间删除成功');
        router.visit(route('workspaces.index'));
    } catch (error: any) {
        toast.error(error.response?.data?.message || '删除失败，请稍后重试');
    } finally {
        isDeleting.value = false;
    }
};

const handleSetCurrent = async () => {
    isSettingCurrent.value = true;

    try {
        await axios.post(`/api/workspaces/${workspace.id}/set-current`);
        toast.success('已切换到当前工作空间');
        // 刷新页面以更新状态
        router.reload();
    } catch (error: any) {
        toast.error(error.response?.data?.message || '切换失败，请稍后重试');
    } finally {
        isSettingCurrent.value = false;
    }
};

const loadClusterPricing = async () => {
    if (!workspace.cluster?.id) return;

    loadingPricing.value = true;
    try {
        const response = await axios.get(`/api/pricing/cluster/${workspace.cluster.id}`);
        clusterPricing.value = response.data;
    } catch (error) {
        console.error('加载集群价格失败:', error);
        clusterPricing.value = null;
    } finally {
        loadingPricing.value = false;
    }
};

const formatPrice = (price: string) => {
    const numPrice = parseFloat(price);
    return numPrice.toFixed(4);
};

// 页面加载时获取价格信息
onMounted(() => {
    loadClusterPricing();
});
</script>

<template>
    <Head :title="workspace.name" />

    <AppLayout>
        <div class="space-y-6 p-4">
            <!-- 页面标题和操作 -->
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <Button variant="ghost" size="sm" as-child>
                        <Link :href="route('workspaces.index')">
                            <ArrowLeft class="mr-2 h-4 w-4" />
                            返回
                        </Link>
                    </Button>
                    <div>
                        <div class="flex items-center gap-3">
                            <h1 class="text-3xl font-bold tracking-tight">{{ workspace.name }}</h1>
                            <Badge :class="getStatusColor(workspace.status)">
                                {{ getStatusText(workspace.status) }}
                            </Badge>
                        </div>
                        <p class="text-muted-foreground">工作空间详情和管理</p>
                    </div>
                </div>

                <div class="flex gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        @click="handleSetCurrent"
                        :disabled="isSettingCurrent || auth.user.current_workspace_id === workspace.id"
                    >
                        <Star v-if="!isSettingCurrent" class="mr-2 h-4 w-4" />
                        {{ isSettingCurrent ? '设置中...' : '设为当前' }}
                        <span v-if="auth.user.current_workspace_id === workspace.id" class="text-xs text-gray-500">（当前）</span>
                    </Button>

                    <Button variant="outline" size="sm" as-child>
                        <Link :href="route('workspaces.edit', { workspace: workspace.id })">
                            <Edit class="mr-2 h-4 w-4" />
                            编辑
                        </Link>
                    </Button>

                    <Dialog>
                        <DialogTrigger as-child>
                            <Button variant="destructive" size="sm">
                                <Trash2 class="mr-2 h-4 w-4" />
                                删除
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>确认删除工作空间</DialogTitle>
                                <DialogDescription> 您确定要删除工作空间 "{{ workspace.name }}" 吗？此操作无法撤销。 </DialogDescription>
                            </DialogHeader>
                            <DialogFooter>
                                <DialogClose as-child>
                                    <Button variant="outline">取消</Button>
                                </DialogClose>
                                <Button variant="destructive" @click="handleDelete" :disabled="isDeleting">
                                    {{ isDeleting ? '删除中...' : '确认删除' }}
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>

                    <!-- 转让所有权确认对话框 -->
                    <Dialog v-model:open="showTransferDialog">
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>确认转让所有权</DialogTitle>
                                <DialogDescription>
                                    您确定要将工作空间所有权转让给该用户吗？此操作不可撤销。转移所有权后，工作空间的计费将转移到该用户下。
                                </DialogDescription>
                            </DialogHeader>
                            <DialogFooter>
                                <DialogClose as-child>
                                    <Button variant="outline">取消</Button>
                                </DialogClose>
                                <Button variant="destructive" @click="confirmTransferOwnership"> 确认转让 </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                </div>
            </div>

            <div class="grid gap-6 lg:grid-cols-3">
                <!-- 主要内容 -->
                <div class="space-y-6 lg:col-span-2">
                    <Tabs default-value="overview" class="w-full">
                        <TabsList class="grid w-full grid-cols-2">
                            <TabsTrigger value="overview">概览</TabsTrigger>
                            <TabsTrigger value="members" class="flex items-center gap-2">
                                <Users class="h-4 w-4" />
                                成员 ({{ props.members.length + 1 }})
                            </TabsTrigger>
                        </TabsList>

                        <TabsContent value="overview" class="space-y-6">
                            <!-- 基本信息 -->
                            <Card>
                                <CardHeader>
                                    <CardTitle>基本信息</CardTitle>
                                    <CardDescription>工作空间的基本配置信息</CardDescription>
                                </CardHeader>
                                <CardContent class="space-y-4">
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="text-sm font-medium text-muted-foreground">工作空间名称</label>
                                            <p class="font-mono text-sm">{{ workspace.name }}</p>
                                        </div>
                                        <div>
                                            <label class="text-sm font-medium text-muted-foreground">集群</label>
                                            <p class="font-mono text-sm">{{ workspace.cluster.name }}</p>
                                        </div>
                                        <div>
                                            <label class="text-sm font-medium text-muted-foreground">状态</label>
                                            <Badge :class="getStatusColor(workspace.status)" class="w-fit">
                                                {{ getStatusText(workspace.status) }}
                                            </Badge>
                                        </div>
                                        <div>
                                            <label class="text-sm font-medium text-muted-foreground">创建时间</label>
                                            <p class="text-sm">{{ formatDate(workspace.created_at) }}</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <!-- 集群定价信息 -->
                            <Card v-if="clusterPricing && clusterPricing.pricing" class="border-blue-200 dark:border-blue-800">
                                <CardHeader>
                                    <CardTitle class="flex items-center gap-2 text-blue-800 dark:text-blue-200"> 集群定价信息 </CardTitle>
                                    <CardDescription>当前集群的资源定价标准</CardDescription>
                                </CardHeader>
                                <CardContent class="space-y-4">
                                    <div v-if="loadingPricing" class="py-4 text-center">
                                        <div class="text-sm text-muted-foreground">加载价格信息中...</div>
                                    </div>
                                    <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-3">
                                        <div v-if="clusterPricing.pricing.memory_gb" class="text-center">
                                            <div class="text-2xl font-bold text-blue-600">
                                                ¥{{ formatPrice(clusterPricing.pricing.memory_gb.price_per_hour) }}
                                            </div>
                                            <div class="text-sm text-muted-foreground">内存/GB/小时</div>
                                        </div>
                                        <div v-if="clusterPricing.pricing.cpu_core" class="text-center">
                                            <div class="text-2xl font-bold text-green-600">
                                                ¥{{ formatPrice(clusterPricing.pricing.cpu_core.price_per_hour) }}
                                            </div>
                                            <div class="text-sm text-muted-foreground">CPU/核/小时</div>
                                        </div>
                                        <div v-if="clusterPricing.pricing.storage_gb" class="text-center">
                                            <div class="text-2xl font-bold text-purple-600">
                                                ¥{{ formatPrice(clusterPricing.pricing.storage_gb.price_per_hour) }}
                                            </div>
                                            <div class="text-sm text-muted-foreground">存储/GB/小时</div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <!-- 暂停信息 -->
                            <Alert v-if="workspace.status === 'suspended'" variant="destructive" class="mb-6">
                                <AlertCircle class="h-4 w-4" />
                                <AlertTitle>工作空间已暂停</AlertTitle>
                                <AlertDescription class="mt-2 space-y-2">
                                    <div v-if="workspace.suspension_reason">
                                        <p class="font-medium">暂停原因：</p>
                                        <p class="text-sm">{{ workspace.suspension_reason }}</p>
                                    </div>
                                    <div v-if="workspace.overdue_amount">
                                        <p class="font-medium">欠费金额：</p>
                                        <p class="text-sm">¥{{ workspace.overdue_amount }}</p>
                                    </div>
                                    <div v-if="workspace.suspended_at">
                                        <p class="font-medium">暂停时间：</p>
                                        <p class="text-sm">{{ formatDate(workspace.suspended_at) }}</p>
                                    </div>
                                </AlertDescription>
                            </Alert>
                        </TabsContent>

                        <TabsContent value="members" class="space-y-6">
                            <!-- 邀请新成员 -->
                            <Card>
                                <CardHeader>
                                    <CardTitle class="flex items-center gap-2">
                                        <Plus class="h-5 w-5" />
                                        邀请成员
                                    </CardTitle>
                                    <CardDescription>邀请其他用户加入此工作空间</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <form @submit.prevent="inviteUser" class="space-y-4">
                                        <div>
                                            <Label for="email">邮箱地址</Label>
                                            <Input id="email" v-model="inviteForm.email" type="email" placeholder="输入要邀请的用户邮箱" required />
                                        </div>
                                        <Button type="submit" :disabled="inviteForm.processing">
                                            <Mail class="mr-2 h-4 w-4" />
                                            {{ inviteForm.processing ? '发送中...' : '发送邀请' }}
                                        </Button>
                                    </form>
                                </CardContent>
                            </Card>

                            <!-- 待处理邀请 -->
                            <Card v-if="props.invitations.length > 0">
                                <CardHeader>
                                    <CardTitle>待处理邀请</CardTitle>
                                    <CardDescription>已发送但尚未接受的邀请</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div class="space-y-3">
                                        <div
                                            v-for="invitation in props.invitations"
                                            :key="invitation.id"
                                            class="flex items-center justify-between rounded-lg border p-3"
                                        >
                                            <div class="flex items-center space-x-3">
                                                <div class="rounded-full bg-gray-100 p-2">
                                                    <Mail class="h-4 w-4 text-gray-600" />
                                                </div>
                                                <div>
                                                    <p class="font-medium">{{ invitation.email }}</p>
                                                    <p class="text-sm text-muted-foreground">邀请于 {{ formatDate(invitation.created_at) }}</p>
                                                </div>
                                            </div>
                                            <Button variant="outline" size="sm" @click="cancelInvitation(invitation.id)"> 取消邀请 </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <!-- 当前成员 -->
                            <Card>
                                <CardHeader>
                                    <CardTitle>工作空间成员</CardTitle>
                                    <CardDescription>管理工作空间的所有成员</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div class="space-y-3">
                                        <!-- 所有者 -->
                                        <div
                                            class="flex items-center justify-between rounded-lg border border-yellow-200 bg-yellow-50 p-3 dark:border-yellow-800 dark:bg-yellow-900/20"
                                        >
                                            <div class="flex items-center space-x-3">
                                                <div class="rounded-full bg-yellow-100 p-2 dark:bg-yellow-900">
                                                    <Crown class="h-4 w-4 text-yellow-600" />
                                                </div>
                                                <div>
                                                    <p class="font-medium">{{ workspace.user?.name || '未知用户' }}</p>
                                                    <p class="text-sm text-muted-foreground">{{ workspace.user?.email }}</p>
                                                </div>
                                            </div>
                                            <Badge variant="secondary" class="bg-yellow-100 text-yellow-800"> 所有者 </Badge>
                                        </div>

                                        <!-- 普通成员 -->
                                        <div
                                            v-for="member in props.members"
                                            :key="member.id"
                                            class="flex items-center justify-between rounded-lg border p-3"
                                        >
                                            <div class="flex items-center space-x-3">
                                                <div class="rounded-full bg-gray-100 p-2">
                                                    <Users class="h-4 w-4 text-gray-600" />
                                                </div>
                                                <div>
                                                    <p class="font-medium">{{ member.name }}</p>
                                                    <p class="text-sm text-muted-foreground">{{ member.email }}</p>
                                                </div>
                                            </div>
                                            <div class="flex items-center gap-2" v-if="workspace.user_id === props.auth.user.id">
                                                <Button variant="outline" size="sm" @click="transferOwnership(member.id)">
                                                    <Crown class="mr-1 h-3 w-3" />
                                                    转让所有权
                                                </Button>
                                                <Button variant="outline" size="sm" @click="removeMember(member.id)">
                                                    <UserMinus class="mr-1 h-3 w-3" />
                                                    移除
                                                </Button>
                                            </div>
                                        </div>

                                        <div v-if="props.members.length === 0" class="py-8 text-center text-muted-foreground">暂无其他成员</div>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                </div>

                <!-- 侧边栏 -->
                <div class="space-y-6">
                    <!-- 快速操作 -->
                    <Card>
                        <CardHeader>
                            <CardTitle class="text-lg">快速操作</CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-3">
                            <Button variant="outline" class="w-full justify-start" @click="handleSetCurrent" :disabled="isSettingCurrent">
                                <Star class="mr-2 h-4 w-4" />
                                设为当前工作空间
                            </Button>

                            <Button variant="outline" class="w-full justify-start" as-child>
                                <Link :href="route('workspaces.edit', { workspace: workspace.id })">
                                    <Edit class="mr-2 h-4 w-4" />
                                    编辑工作空间
                                </Link>
                            </Button>

                            <!-- <Button variant="outline" class="w-full justify-start">
                                <Settings class="mr-2 h-4 w-4" />
                                高级设置
                            </Button> -->
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
