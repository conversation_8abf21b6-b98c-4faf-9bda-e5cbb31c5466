<script setup lang="ts">
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, router } from '@inertiajs/vue3';
import { AlertCircle, Check, Mail, X } from 'lucide-vue-next';
import { toast } from 'sonner';
import { ref } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    invitations: any[];
}

const props = defineProps<Props>();

const showRejectDialog = ref(false);
const rejectInvitationId = ref<number | null>(null);

const acceptInvitation = (invitationId: number) => {
    router.post(
        route('workspace-invitations.accept', invitationId),
        {},
        {
            onSuccess: () => {
                toast.success('邀请已接受，欢迎加入工作空间！');
            },
            onError: (errors) => {
                toast.error('接受邀请失败，请重试');
            },
        },
    );
};

const rejectInvitation = (invitationId: number) => {
    rejectInvitationId.value = invitationId;
    showRejectDialog.value = true;
};

const confirmRejectInvitation = () => {
    if (!rejectInvitationId.value) return;

    router.post(
        route('workspace-invitations.reject', rejectInvitationId.value),
        {},
        {
            onSuccess: () => {
                toast.success('邀请已拒绝');
                showRejectDialog.value = false;
                rejectInvitationId.value = null;
            },
        },
    );
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
};
</script>

<template>
    <Head title="我的邀请" />

    <AppLayout>
        <div class="space-y-6 p-4">
            <!-- 页面标题 -->
            <div>
                <h1 class="text-3xl font-bold tracking-tight">我的邀请</h1>
                <p class="text-muted-foreground">查看和管理您收到的工作空间邀请</p>
            </div>

            <!-- 邀请列表 -->
            <div class="space-y-4">
                <Card v-if="props.invitations.length === 0">
                    <CardContent class="flex flex-col items-center justify-center py-16">
                        <Mail class="mb-4 h-12 w-12 text-muted-foreground/50" />
                        <h3 class="mb-2 text-lg font-medium">没有待处理的邀请</h3>
                        <p class="text-center text-sm text-muted-foreground">当有人邀请您加入工作空间时，邀请会显示在这里</p>
                    </CardContent>
                </Card>

                <Card v-for="invitation in props.invitations" :key="invitation.id">
                    <CardHeader>
                        <div class="flex items-start justify-between">
                            <div>
                                <CardTitle class="flex items-center gap-2">
                                    <Mail class="h-5 w-5" />
                                    加入 "{{ invitation.workspace.name }}" 工作空间
                                </CardTitle>
                                <CardDescription class="mt-1">
                                    {{ invitation.sender?.name || invitation.workspace.user?.name || '未知用户' }} 邀请您加入他们的工作空间
                                </CardDescription>
                            </div>
                            <Badge variant="outline" class="bg-blue-50 text-blue-700"> 待处理 </Badge>
                        </div>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <Alert>
                            <AlertCircle class="h-4 w-4" />
                            <AlertTitle>工作空间邀请</AlertTitle>
                            <AlertDescription> 您被邀请加入 "{{ invitation.workspace.name }}" 工作空间，请选择接受或拒绝此邀请。 </AlertDescription>
                        </Alert>

                        <div class="rounded-lg border bg-muted/50 p-3">
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="font-medium text-muted-foreground">工作空间：</span>
                                    <span>{{ invitation.workspace.name }}</span>
                                </div>
                                <div>
                                    <span class="font-medium text-muted-foreground">邀请时间：</span>
                                    <span>{{ formatDate(invitation.created_at) }}</span>
                                </div>
                                <div>
                                    <span class="font-medium text-muted-foreground">邀请者：</span>
                                    <span>{{ invitation.sender?.name || invitation.workspace.user?.name || '未知' }}</span>
                                </div>
                                <div v-if="invitation.workspace.namespace">
                                    <span class="font-medium text-muted-foreground">工作空间：</span>
                                    <span class="font-mono text-xs">{{ invitation.workspace.name }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="flex gap-2">
                            <Button @click="acceptInvitation(invitation.id)" class="flex-1">
                                <Check class="mr-2 h-4 w-4" />
                                接受邀请
                            </Button>
                            <Button variant="outline" @click="rejectInvitation(invitation.id)" class="flex-1">
                                <X class="mr-2 h-4 w-4" />
                                拒绝邀请
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- 拒绝邀请确认对话框 -->
            <Dialog v-model:open="showRejectDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>确认拒绝邀请</DialogTitle>
                        <DialogDescription> 您确定要拒绝这个邀请吗？拒绝后无法撤销。 </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <DialogClose as-child>
                            <Button variant="outline">取消</Button>
                        </DialogClose>
                        <Button variant="destructive" @click="confirmRejectInvitation"> 确认拒绝 </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    </AppLayout>
</template>
