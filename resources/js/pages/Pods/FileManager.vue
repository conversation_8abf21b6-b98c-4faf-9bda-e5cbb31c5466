<template>
    <AppLayout>

        <Head title="文件管理器" />

        <div class="space-y-6 p-4">

            <PodFileManager />
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import PodFileManager from '@/components/PodFileManager.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';

interface Props {
    podName?: string;
}

defineProps<Props>();
</script>
